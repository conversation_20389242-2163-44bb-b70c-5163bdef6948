# ChessHub - Modern Chess Website

A modern, real-time multiplayer chess website built with Next.js 15, React 18, Tailwind CSS, and Supabase.

## Features

- 🎯 **Real-time Multiplayer**: Play chess with friends online with instant move synchronization and intelligent connection management
- 🔐 **Google OAuth Authentication**: Secure sign-in with Google accounts
- 📊 **Match History & Stats**: Track your games, wins, losses, and performance
- 💬 **In-game Chat**: Communicate with your opponent during games
- 📱 **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- 🌙 **Dark Mode**: Toggle between light and dark themes
- ⚡ **Fast & Modern**: Built with the latest web technologies

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Real-time, Auth)
- **Chess Logic**: chess.js, react-chessboard
- **Icons**: Lucide React
- **Deployment**: Vercel

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
