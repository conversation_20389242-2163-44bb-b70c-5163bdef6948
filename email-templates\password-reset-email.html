<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Chess Web Password</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
            padding: 40px 30px;
            text-align: center;
        }
        .logo {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }
        .crown-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .brand-name {
            font-size: 28px;
            font-weight: bold;
            color: white;
            margin: 0;
        }
        .header-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            margin: 10px 0 0 0;
        }
        .content {
            padding: 40px 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 20px 0;
            text-align: center;
        }
        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 30px;
            text-align: center;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .instructions {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .instructions-title {
            font-weight: 600;
            color: #0c4a6e;
            margin: 0 0 12px 0;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .instructions-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .instruction-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 14px;
            color: #0c4a6e;
        }
        .step-number {
            background-color: #0ea5e9;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        .security-warning {
            background-color: #fef2f2;
            border: 1px solid #fca5a5;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-warning-title {
            font-weight: 600;
            color: #dc2626;
            margin: 0 0 8px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .security-warning-text {
            font-size: 13px;
            color: #dc2626;
            margin: 0;
        }
        .footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer-text {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 10px 0;
        }
        .footer-link {
            color: #3b82f6;
            text-decoration: none;
        }
        .alternative-text {
            background-color: #f9fafb;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
            font-size: 14px;
            color: #6b7280;
        }
        .alternative-text strong {
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <div class="crown-icon">♔</div>
                <h1 class="brand-name">Chess Web</h1>
            </div>
            <p class="header-subtitle">Password Reset Request</p>
        </div>
        
        <div class="content">
            <h2 class="title">Reset Your Password 🔐</h2>
            
            <p class="message">
                We received a request to reset your Chess Web account password. Click the button below to create a new password.
            </p>
            
            <div class="cta-container">
                <a href="{{ .ConfirmationURL }}" class="cta-button">
                    🔑 Reset My Password
                </a>
            </div>
            
            <div class="instructions">
                <h3 class="instructions-title">
                    📋 How to reset your password:
                </h3>
                <ol class="instructions-list">
                    <li class="instruction-item">
                        <div class="step-number">1</div>
                        Click the "Reset My Password" button above
                    </li>
                    <li class="instruction-item">
                        <div class="step-number">2</div>
                        You'll be taken to a secure page on Chess Web
                    </li>
                    <li class="instruction-item">
                        <div class="step-number">3</div>
                        Enter your new password (minimum 6 characters)
                    </li>
                    <li class="instruction-item">
                        <div class="step-number">4</div>
                        Confirm your new password and save changes
                    </li>
                    <li class="instruction-item">
                        <div class="step-number">5</div>
                        Sign in with your new password and continue playing!
                    </li>
                </ol>
            </div>
            
            <div class="security-warning">
                <p class="security-warning-title">
                    ⚠️ Important Security Information
                </p>
                <p class="security-warning-text">
                    This password reset link will expire in 1 hour for your security. If you didn't request this reset, please ignore this email and your password will remain unchanged. Consider enabling two-factor authentication for added security.
                </p>
            </div>
            
            <div class="alternative-text">
                <strong>Can't click the button?</strong> Copy and paste this link into your browser:<br>
                <span style="word-break: break-all; color: #3b82f6;">{{ .ConfirmationURL }}</span>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                If you're having trouble, contact our support team at <a href="mailto:<EMAIL>" class="footer-link"><EMAIL></a>
            </p>
            <p class="footer-text">
                Chess Web - Secure Gaming Experience
            </p>
        </div>
    </div>
</body>
</html>
