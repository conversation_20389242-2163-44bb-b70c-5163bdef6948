<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Chess Web - Confirm Your Email</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            padding: 40px 30px;
            text-align: center;
        }
        .logo {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }
        .crown-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .brand-name {
            font-size: 28px;
            font-weight: bold;
            color: white;
            margin: 0;
        }
        .header-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            margin: 10px 0 0 0;
        }
        .content {
            padding: 40px 30px;
        }
        .welcome-title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 20px 0;
            text-align: center;
        }
        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 30px;
            text-align: center;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.2s ease;
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .features {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 24px;
            margin: 30px 0;
        }
        .features-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 16px 0;
            text-align: center;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            color: #4b5563;
        }
        .feature-icon {
            width: 20px;
            height: 20px;
            background-color: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 12px;
        }
        .footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer-text {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 10px 0;
        }
        .footer-link {
            color: #3b82f6;
            text-decoration: none;
        }
        .security-note {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-note-title {
            font-weight: 600;
            color: #92400e;
            margin: 0 0 8px 0;
            font-size: 14px;
        }
        .security-note-text {
            font-size: 13px;
            color: #92400e;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <div class="crown-icon">♔</div>
                <h1 class="brand-name">Chess Web</h1>
            </div>
            <p class="header-subtitle">Master the Art of Chess</p>
        </div>
        
        <div class="content">
            <h2 class="welcome-title">Welcome to Chess Web! 🎉</h2>
            
            <p class="message">
                Thank you for joining our chess community! You're just one click away from starting your chess journey with thousands of players worldwide.
            </p>
            
            <div class="cta-container">
                <a href="{{ .ConfirmationURL }}" class="cta-button">
                    ✓ Confirm Your Email Address
                </a>
            </div>
            
            <div class="features">
                <h3 class="features-title">🎮 What awaits you:</h3>
                <ul class="feature-list">
                    <li class="feature-item">
                        <div class="feature-icon">⚡</div>
                        Real-time multiplayer chess games
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon">📊</div>
                        Track your progress and game history
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon">💬</div>
                        Chat with opponents during games
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon">🏆</div>
                        Climb leaderboards and earn achievements
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon">🎯</div>
                        Improve your skills with game analysis
                    </li>
                </ul>
            </div>
            
            <div class="security-note">
                <p class="security-note-title">🔒 Security Note</p>
                <p class="security-note-text">
                    This confirmation link will expire in 24 hours for your security. If you didn't create this account, you can safely ignore this email.
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Need help? Visit our <a href="{{ .SiteURL }}/help" class="footer-link">Help Center</a> or reply to this email.
            </p>
            <p class="footer-text">
                Chess Web - Where Strategy Meets Community
            </p>
        </div>
    </div>
</body>
</html>
